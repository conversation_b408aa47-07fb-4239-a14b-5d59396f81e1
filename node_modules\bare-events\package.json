{"name": "bare-events", "version": "2.5.0", "description": "Event emitters for JavaScript", "exports": {".": "./index.js", "./package": "./package.json", "./errors": "./lib/errors.js"}, "files": ["index.js", "lib"], "scripts": {"test": "npm run lint && npm run test:bare && npm run test:node", "test:bare": "bare test.js", "test:node": "node test.js", "lint": "standard"}, "repository": {"type": "git", "url": "git+https://github.com/holepunchto/bare-events.git"}, "author": "Holepunch", "license": "Apache-2.0", "bugs": {"url": "https://github.com/holepunchto/bare-events/issues"}, "homepage": "https://github.com/holepunchto/bare-events#readme", "devDependencies": {"brittle": "^3.3.2", "standard": "^17.0.0"}}